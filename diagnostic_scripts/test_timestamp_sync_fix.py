#!/usr/bin/env python3
"""
🔥 测试时间戳同步修复效果
验证网络故障容错机制是否正常工作
"""

import sys
import os
import asyncio
import time

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "123")
sys.path.insert(0, project_root)
os.chdir(project_root)

async def test_timestamp_sync_fix():
    """测试时间戳同步修复"""
    print("🔥 测试时间戳同步修复效果...")
    print("=" * 60)
    
    try:
        from websocket.unified_timestamp_processor import initialize_all_timestamp_processors, check_all_timestamp_sync_health
        
        # 1. 强制执行时间同步
        print("🔍 1. 执行强制时间同步...")
        sync_results = await initialize_all_timestamp_processors(force_sync=True)
        
        print("同步结果:")
        for exchange, success in sync_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {exchange.upper()}: {status}")
        
        # 2. 检查同步后的健康状态
        print("\n🔍 2. 检查同步后的健康状态...")
        health_status = await check_all_timestamp_sync_health()
        
        print("健康状态:")
        for exchange, status in health_status.items():
            print(f"  {exchange.upper()}:")
            print(f"    时间已同步: {status.get('time_synced', False)}")
            print(f"    健康级别: {status.get('health_level', 'UNKNOWN')}")
            print(f"    时间偏移: {status.get('time_offset_ms', 0)}ms")
            print(f"    同步年龄: {status.get('sync_age_seconds', -1)}秒")
        
        # 3. 统计修复效果
        healthy_count = sum(1 for status in health_status.values() if status.get('is_healthy', False))
        synced_count = sum(1 for status in health_status.values() if status.get('time_synced', False))
        
        print(f"\n🎯 修复效果统计:")
        print(f"  健康的交易所: {healthy_count}/3")
        print(f"  已同步的交易所: {synced_count}/3")
        
        if synced_count == 3:
            print("✅ 时间戳同步修复完全成功！")
            return True
        elif synced_count > 0:
            print("⚠️ 时间戳同步修复部分成功")
            return True
        else:
            print("❌ 时间戳同步修复失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

async def main():
    """主函数"""
    success = await test_timestamp_sync_fix()
    
    if success:
        print("\n🎉 时间戳同步修复测试通过！")
        print("💡 网络故障容错机制正常工作，系统可用性得到保障")
    else:
        print("\n⚠️ 时间戳同步修复需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
