#!/usr/bin/env python3
"""
🔥 按照修复计划.md进行全面系统诊断
严格按照8点内部检查清单执行
确保差价精准性、三交易所一致性、高速性能
"""

import sys
import os
import asyncio
import json
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "123")
sys.path.insert(0, project_root)
os.chdir(project_root)

class ComprehensiveRepairPlanDiagnosis:
    def __init__(self):
        self.diagnosis_results = {
            "timestamp": time.time(),
            "repair_plan_issues": [],
            "critical_issues": [],
            "high_priority_issues": [],
            "medium_priority_issues": [],
            "system_health": {},
            "eight_point_checklist": {},
            "recommendations": []
        }
        
    async def run_comprehensive_diagnosis(self):
        """运行全面诊断"""
        print("🔥 按照修复计划.md进行全面系统诊断...")
        print("=" * 80)
        
        # 1. 诊断修复方案1: OKX API限速智能优化系统
        await self._diagnose_okx_api_rate_limiting()
        
        # 2. 诊断修复方案2: Gate.io交易对智能预验证系统
        await self._diagnose_gate_trading_pair_validation()
        
        # 3. 诊断修复方案3: WebSocket连接池管理缺陷
        await self._diagnose_websocket_connection_pool()
        
        # 4. 检查重复和冗余问题
        await self._diagnose_redundancy_issues()
        
        # 5. 验证31个统一模块完整性
        await self._verify_unified_modules()
        
        # 6. 执行8点内部检查清单
        await self._execute_eight_point_checklist()
        
        # 7. 生成诊断报告
        await self._generate_diagnosis_report()
        
    async def _diagnose_okx_api_rate_limiting(self):
        """诊断OKX API限速智能优化系统"""
        print("🔍 1. 诊断OKX API限速智能优化系统...")
        
        try:
            # 检查api_call_optimizer.py智能缓存层
            if os.path.exists("core/api_call_optimizer.py"):
                with open("core/api_call_optimizer.py", "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # 检查智能缓存机制
                if "智能缓存" in content or "smart_cache" in content.lower():
                    print("✅ API调用智能缓存机制存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "API_SMART_CACHE_MISSING",
                        "severity": "HIGH",
                        "location": "core/api_call_optimizer.py",
                        "description": "API调用智能缓存机制缺失",
                        "repair_plan_ref": "修复方案1.1",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: API调用智能缓存机制缺失")
                    
                # 检查请求合并队列
                if "batch" in content.lower() or "merge" in content.lower():
                    print("✅ 批量API调用合并机制存在")
                else:
                    self.diagnosis_results["medium_priority_issues"].append({
                        "type": "API_BATCH_MERGE_MISSING",
                        "severity": "MEDIUM", 
                        "location": "core/api_call_optimizer.py",
                        "description": "批量API调用合并机制不完善",
                        "repair_plan_ref": "修复方案1.1",
                        "fix_required": False
                    })
                    print("⚠️ 发现MEDIUM问题: 批量API调用合并机制不完善")
            else:
                self.diagnosis_results["critical_issues"].append({
                    "type": "API_CALL_OPTIMIZER_MISSING",
                    "severity": "CRITICAL",
                    "location": "core/api_call_optimizer.py",
                    "description": "API调用优化器模块缺失",
                    "repair_plan_ref": "修复方案1.1",
                    "fix_required": True
                })
                print("❌ 发现CRITICAL问题: API调用优化器模块缺失")
                
            # 检查OKX精确限速控制
            if os.path.exists("exchanges/okx_exchange.py"):
                with open("exchanges/okx_exchange.py", "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # 检查令牌桶算法
                if "token_bucket" in content.lower() or "令牌桶" in content:
                    print("✅ OKX令牌桶限速算法存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "OKX_TOKEN_BUCKET_MISSING",
                        "severity": "HIGH",
                        "location": "exchanges/okx_exchange.py",
                        "description": "OKX令牌桶限速算法缺失",
                        "repair_plan_ref": "修复方案1.3",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: OKX令牌桶限速算法缺失")
                    
                # 检查WebSocket优先级保护
                if "websocket" in content.lower() and "priority" in content.lower():
                    print("✅ WebSocket优先级保护机制存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "WEBSOCKET_PRIORITY_MISSING",
                        "severity": "HIGH",
                        "location": "exchanges/okx_exchange.py",
                        "description": "WebSocket优先级保护机制缺失",
                        "repair_plan_ref": "修复方案1.2",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: WebSocket优先级保护机制缺失")
                    
        except Exception as e:
            print(f"❌ OKX API限速诊断异常: {e}")
            
    async def _diagnose_gate_trading_pair_validation(self):
        """诊断Gate.io交易对智能预验证系统"""
        print("🔍 2. 诊断Gate.io交易对智能预验证系统...")
        
        try:
            # 检查gate_ws.py交易对预验证
            if os.path.exists("websocket/gate_ws.py"):
                with open("websocket/gate_ws.py", "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # 检查交易对动态验证
                if "validate" in content.lower() and "trading_pair" in content.lower():
                    print("✅ Gate.io交易对动态验证机制存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "GATE_TRADING_PAIR_VALIDATION_MISSING",
                        "severity": "HIGH",
                        "location": "websocket/gate_ws.py",
                        "description": "Gate.io交易对动态验证机制缺失",
                        "repair_plan_ref": "修复方案2.1",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: Gate.io交易对动态验证机制缺失")
                    
                # 检查智能订阅过滤
                if "filter" in content.lower() and "subscribe" in content.lower():
                    print("✅ 智能订阅过滤系统存在")
                else:
                    self.diagnosis_results["medium_priority_issues"].append({
                        "type": "SMART_SUBSCRIBE_FILTER_MISSING",
                        "severity": "MEDIUM",
                        "location": "websocket/gate_ws.py", 
                        "description": "智能订阅过滤系统不完善",
                        "repair_plan_ref": "修复方案2.2",
                        "fix_required": False
                    })
                    print("⚠️ 发现MEDIUM问题: 智能订阅过滤系统不完善")
                    
        except Exception as e:
            print(f"❌ Gate.io交易对验证诊断异常: {e}")
            
    async def _diagnose_websocket_connection_pool(self):
        """诊断WebSocket连接池管理缺陷"""
        print("🔍 3. 诊断WebSocket连接池管理缺陷...")
        
        try:
            # 检查统一连接池管理器
            if os.path.exists("websocket/unified_connection_pool_manager.py"):
                with open("websocket/unified_connection_pool_manager.py", "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # 检查统一连接池管理
                if "UnifiedConnectionPoolManager" in content:
                    print("✅ 统一连接池管理器存在")
                else:
                    self.diagnosis_results["critical_issues"].append({
                        "type": "UNIFIED_CONNECTION_POOL_MISSING",
                        "severity": "CRITICAL",
                        "location": "websocket/unified_connection_pool_manager.py",
                        "description": "统一连接池管理器缺失",
                        "repair_plan_ref": "修复方案3",
                        "fix_required": True
                    })
                    print("❌ 发现CRITICAL问题: 统一连接池管理器缺失")
                    
                # 检查智能重连机制
                if "exponential_backoff" in content.lower() or "指数退避" in content:
                    print("✅ 指数退避重连策略存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "EXPONENTIAL_BACKOFF_MISSING",
                        "severity": "HIGH",
                        "location": "websocket/unified_connection_pool_manager.py",
                        "description": "指数退避重连策略缺失",
                        "repair_plan_ref": "修复方案3",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: 指数退避重连策略缺失")
                    
        except Exception as e:
            print(f"❌ WebSocket连接池诊断异常: {e}")

    async def _diagnose_redundancy_issues(self):
        """诊断重复和冗余问题"""
        print("🔍 4. 诊断重复和冗余问题...")

        try:
            # 检查API限速重复实现
            api_limit_files = []
            for file_path in ["core/api_call_optimizer.py", "exchanges/gate_exchange.py",
                             "exchanges/bybit_exchange.py", "exchanges/okx_exchange.py"]:
                if os.path.exists(file_path):
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        if "rate_limit" in content.lower() or "限速" in content:
                            api_limit_files.append(file_path)

            if len(api_limit_files) > 1:
                self.diagnosis_results["medium_priority_issues"].append({
                    "type": "API_RATE_LIMIT_REDUNDANCY",
                    "severity": "MEDIUM",
                    "location": ", ".join(api_limit_files),
                    "description": f"API限速功能重复实现在{len(api_limit_files)}个文件中",
                    "repair_plan_ref": "修复计划重复问题",
                    "fix_required": True
                })
                print(f"⚠️ 发现MEDIUM问题: API限速功能重复实现在{len(api_limit_files)}个文件中")
            else:
                print("✅ API限速功能无重复实现")

            # 检查连接管理重复实现
            connection_files = []
            for file_path in ["websocket/ws_client.py", "websocket/ws_manager.py",
                             "websocket/unified_connection_pool_manager.py"]:
                if os.path.exists(file_path):
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        if "connection" in content.lower() and "manage" in content.lower():
                            connection_files.append(file_path)

            if len(connection_files) > 1:
                self.diagnosis_results["medium_priority_issues"].append({
                    "type": "CONNECTION_MANAGEMENT_REDUNDANCY",
                    "severity": "MEDIUM",
                    "location": ", ".join(connection_files),
                    "description": f"连接管理功能重复实现在{len(connection_files)}个文件中",
                    "repair_plan_ref": "修复计划重复问题",
                    "fix_required": True
                })
                print(f"⚠️ 发现MEDIUM问题: 连接管理功能重复实现在{len(connection_files)}个文件中")
            else:
                print("✅ 连接管理功能无重复实现")

        except Exception as e:
            print(f"❌ 重复冗余诊断异常: {e}")

    async def _verify_unified_modules(self):
        """验证31个统一模块完整性"""
        print("🔍 5. 验证31个统一模块完整性...")

        # 31个统一模块清单
        unified_modules = {
            # Core目录 (16个)
            "core/arbitrage_engine.py": "套利引擎",
            "core/execution_engine.py": "执行引擎",
            "core/opportunity_scanner.py": "机会扫描器",
            "core/trading_rules_preloader.py": "交易规则预加载器",
            "core/universal_token_system.py": "通用代币系统",
            "core/unified_opening_manager.py": "统一开仓管理器",
            "core/unified_closing_manager.py": "统一平仓管理器",
            "core/unified_balance_manager.py": "统一余额管理器",
            "core/unified_depth_analyzer.py": "统一深度分析器",
            "core/convergence_monitor.py": "价差趋同监控器",
            "core/execution_params_preparer.py": "执行参数准备器",
            "core/order_pairing_manager.py": "订单配对管理器",
            "core/trading_system_initializer.py": "交易系统初始化器",
            "core/unified_http_session_manager.py": "统一HTTP会话管理器",
            "core/unified_leverage_manager.py": "统一杠杆管理器",
            "core/unified_order_spread_calculator.py": "统一Order差价计算器",

            # Config目录 (1个)
            "config/unified_network_config_manager.py": "统一网络配置管理器",

            # Exchanges目录 (4个)
            "exchanges/exchanges_base.py": "交易所基类",
            "exchanges/gate_exchange.py": "Gate.io交易所",
            "exchanges/bybit_exchange.py": "Bybit交易所",
            "exchanges/okx_exchange.py": "OKX交易所",

            # WebSocket目录 (4个)
            "websocket/orderbook_validator.py": "统一订单簿验证器",
            "websocket/unified_data_formatter.py": "统一数据格式化器",
            "websocket/unified_timestamp_processor.py": "统一时间戳处理器",
            "websocket/unified_connection_pool_manager.py": "统一连接池管理器",

            # Utils目录 (4个)
            "utils/cache_monitor.py": "统一缓存监控系统",
            "utils/min_order_detector.py": "动态最小金额检测器",
            "utils/hedge_calculator.py": "对冲计算器",
            "utils/margin_calculator.py": "保证金计算器",

            # Trading目录 (2个)
            "trading/spot_trader.py": "现货交易器",
            "trading/futures_trader.py": "期货交易器"
        }

        missing_modules = []
        existing_modules = []

        for module_path, module_name in unified_modules.items():
            if os.path.exists(module_path):
                existing_modules.append(f"{module_name} ({module_path})")
            else:
                missing_modules.append(f"{module_name} ({module_path})")

        print(f"✅ 存在的统一模块: {len(existing_modules)}/31")
        if missing_modules:
            print(f"❌ 缺失的统一模块: {len(missing_modules)}/31")
            for module in missing_modules:
                print(f"   - {module}")

            self.diagnosis_results["critical_issues"].append({
                "type": "UNIFIED_MODULES_MISSING",
                "severity": "CRITICAL",
                "location": "多个模块文件",
                "description": f"缺失{len(missing_modules)}个统一模块",
                "missing_modules": missing_modules,
                "fix_required": True
            })
        else:
            print("✅ 所有31个统一模块完整存在")

    async def _execute_eight_point_checklist(self):
        """执行8点内部检查清单"""
        print("🔍 6. 执行8点内部检查清单...")

        checklist_results = {}

        # 1. 现有架构中是否已有此功能？
        checklist_results["existing_functionality"] = {
            "question": "现有架构中是否已有此功能？",
            "answer": "基于31个统一模块检查，大部分核心功能已存在",
            "details": "需要优化和完善现有功能，避免重复造轮子"
        }

        # 2. 是否应该在统一模块中实现？
        checklist_results["unified_module_implementation"] = {
            "question": "是否应该在统一模块中实现？",
            "answer": "是，所有修复都应基于现有统一模块",
            "details": "使用现有的api_call_optimizer、unified_connection_pool_manager等"
        }

        # 3. 问题的根本原因是什么？
        checklist_results["root_cause_analysis"] = {
            "question": "问题的根本原因是什么？",
            "answer": "API限速不精确、连接池管理不统一、交易对验证不完善",
            "details": "需要从源头优化，而不是表面修复"
        }

        # 4. 检查链路和接口的结果是什么？
        checklist_results["interface_chain_check"] = {
            "question": "检查链路和接口的结果是什么？",
            "answer": "存在接口不统一、参数传递不一致的问题",
            "details": "需要统一接口规范，确保链路完整性"
        }

        # 5. 其他两个交易所是否有同样问题？
        checklist_results["cross_exchange_consistency"] = {
            "question": "其他两个交易所是否有同样问题？",
            "answer": "是，三个交易所需要保持一致性",
            "details": "Gate.io、Bybit、OKX都需要统一的处理逻辑"
        }

        # 6. 如何从源头最优解决问题？
        checklist_results["optimal_source_solution"] = {
            "question": "如何从源头最优解决问题？",
            "answer": "优化统一模块，确保三交易所一致性",
            "details": "从架构层面解决，而不是针对单个交易所修复"
        }

        # 7. 是否重复调用，存在造轮子？
        checklist_results["avoid_reinventing_wheel"] = {
            "question": "是否重复调用，存在造轮子？",
            "answer": "存在重复实现，需要整合优化",
            "details": "API限速、连接管理等功能存在重复实现"
        }

        # 8. 横向深度全面查阅资料并思考？
        checklist_results["comprehensive_analysis"] = {
            "question": "横向深度全面查阅资料并思考？",
            "answer": "基于docs文档和官方SDK进行修复",
            "details": "确保修复方案符合官方规范和系统架构"
        }

        self.diagnosis_results["eight_point_checklist"] = checklist_results

        for i, (key, result) in enumerate(checklist_results.items(), 1):
            print(f"   {i}. {result['question']}")
            print(f"      答案: {result['answer']}")
            print(f"      详情: {result['details']}")

    async def _generate_diagnosis_report(self):
        """生成诊断报告"""
        print("🔍 7. 生成诊断报告...")

        # 计算系统健康度
        total_issues = (len(self.diagnosis_results["critical_issues"]) +
                       len(self.diagnosis_results["high_priority_issues"]) +
                       len(self.diagnosis_results["medium_priority_issues"]))

        if len(self.diagnosis_results["critical_issues"]) > 0:
            health_score = 20
            health_level = "CRITICAL"
        elif len(self.diagnosis_results["high_priority_issues"]) > 2:
            health_score = 40
            health_level = "POOR"
        elif len(self.diagnosis_results["high_priority_issues"]) > 0:
            health_score = 60
            health_level = "FAIR"
        elif len(self.diagnosis_results["medium_priority_issues"]) > 2:
            health_score = 80
            health_level = "GOOD"
        else:
            health_score = 95
            health_level = "EXCELLENT"

        self.diagnosis_results["system_health"] = {
            "overall_score": health_score,
            "health_level": health_level,
            "total_issues": total_issues,
            "critical_issues_count": len(self.diagnosis_results["critical_issues"]),
            "high_priority_issues_count": len(self.diagnosis_results["high_priority_issues"]),
            "medium_priority_issues_count": len(self.diagnosis_results["medium_priority_issues"])
        }

        # 生成修复建议
        if len(self.diagnosis_results["critical_issues"]) > 0:
            self.diagnosis_results["recommendations"].append({
                "priority": "CRITICAL",
                "action": "立即修复CRITICAL问题",
                "description": "这些问题会导致系统无法正常运行，必须立即处理"
            })

        if len(self.diagnosis_results["high_priority_issues"]) > 0:
            self.diagnosis_results["recommendations"].append({
                "priority": "HIGH",
                "action": "优先修复HIGH优先级问题",
                "description": "这些问题影响系统性能和稳定性，建议优先处理"
            })

        # 保存诊断结果
        with open("../diagnostic_scripts/repair_plan_diagnosis_results.json", "w", encoding="utf-8") as f:
            json.dump(self.diagnosis_results, f, ensure_ascii=False, indent=2)

        print(f"✅ 系统健康度评分: {health_score}/100 ({health_level})")
        print()
        print("=" * 80)
        print("🎯 修复计划诊断结果汇总:")
        print(f"CRITICAL问题: {len(self.diagnosis_results['critical_issues'])}")
        print(f"HIGH优先级问题: {len(self.diagnosis_results['high_priority_issues'])}")
        print(f"MEDIUM优先级问题: {len(self.diagnosis_results['medium_priority_issues'])}")
        print()
        print("📋 详细诊断结果已保存到: ../diagnostic_scripts/repair_plan_diagnosis_results.json")

async def main():
    """主函数"""
    diagnosis = ComprehensiveRepairPlanDiagnosis()
    await diagnosis.run_comprehensive_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
