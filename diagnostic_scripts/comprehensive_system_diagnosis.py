#!/usr/bin/env python3
"""
🔥 综合系统诊断脚本 - 精准定位当前系统问题
基于修复提示词要求，深度审查系统状态
"""

import sys
import os
import time
import json
import asyncio
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "123")
sys.path.insert(0, project_root)
os.chdir(project_root)  # 切换到123目录

class ComprehensiveSystemDiagnosis:
    """综合系统诊断器"""
    
    def __init__(self):
        self.diagnosis_results = {
            "timestamp": time.time(),
            "critical_issues": [],
            "high_priority_issues": [],
            "medium_priority_issues": [],
            "system_health": {},
            "recommendations": []
        }
    
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔥 开始综合系统诊断...")
        print("=" * 60)
        
        # 1. 错误处理器诊断
        await self._diagnose_error_handler()
        
        # 2. WebSocket连接诊断
        await self._diagnose_websocket_connections()
        
        # 3. 时间戳同步诊断
        await self._diagnose_timestamp_sync()
        
        # 4. API限速诊断
        await self._diagnose_api_rate_limits()
        
        # 5. 交易对验证诊断
        await self._diagnose_trading_pairs()
        
        # 6. 系统架构一致性诊断
        await self._diagnose_architecture_consistency()
        
        # 7. 生成诊断报告
        await self._generate_diagnosis_report()
        
        return self.diagnosis_results
    
    async def _diagnose_error_handler(self):
        """诊断错误处理器问题"""
        print("🔍 1. 诊断错误处理器...")
        
        try:
            # 检查错误处理器代码
            error_handler_path = "websocket/error_handler.py"
            if os.path.exists(error_handler_path):
                with open(error_handler_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查第289-290行的统计Bug
                lines = content.split('\n')
                if len(lines) > 290:
                    line_289 = lines[288].strip() if len(lines) > 288 else ""
                    line_290 = lines[289].strip() if len(lines) > 289 else ""
                    
                    if "sum(len(" in line_289 or "sum(len(" in line_290:
                        self.diagnosis_results["critical_issues"].append({
                            "type": "ERROR_HANDLER_BUG",
                            "severity": "CRITICAL",
                            "location": "websocket/error_handler.py:289-290",
                            "description": "错误处理器统计计算Bug - TypeError: 'int' object is not iterable",
                            "evidence": f"Line 289: {line_289}, Line 290: {line_290}",
                            "impact": "错误恢复统计功能完全失效",
                            "fix_required": True
                        })
                        print("❌ 发现CRITICAL问题: 错误处理器统计Bug")
                    else:
                        print("✅ 错误处理器统计逻辑正常")
                
                # 检查是否有其他潜在问题
                if "sum(len([" in content:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "POTENTIAL_STATISTICS_BUG",
                        "severity": "HIGH",
                        "location": "websocket/error_handler.py",
                        "description": "发现其他可能的统计计算错误",
                        "fix_required": True
                    })
            else:
                self.diagnosis_results["critical_issues"].append({
                    "type": "MISSING_ERROR_HANDLER",
                    "severity": "CRITICAL",
                    "description": "错误处理器文件不存在",
                    "fix_required": True
                })
                
        except Exception as e:
            self.diagnosis_results["critical_issues"].append({
                "type": "ERROR_HANDLER_DIAGNOSIS_FAILED",
                "severity": "CRITICAL",
                "description": f"错误处理器诊断失败: {e}",
                "fix_required": True
            })
    
    async def _diagnose_websocket_connections(self):
        """诊断WebSocket连接问题"""
        print("🔍 2. 诊断WebSocket连接...")
        
        try:
            # 检查WebSocket管理器
            ws_manager_path = "websocket/ws_manager.py"
            if os.path.exists(ws_manager_path):
                with open(ws_manager_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查连接注册机制
                if "create_connection" in content and "connection_pool_manager" in content:
                    print("✅ WebSocket连接注册机制存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "WEBSOCKET_REGISTRATION_MISSING",
                        "severity": "HIGH",
                        "location": "websocket/ws_manager.py",
                        "description": "WebSocket连接注册机制缺失",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: WebSocket连接注册机制缺失")
                
                # 检查错误隔离机制
                if "error_count" in content and "isolated_restart" in content:
                    print("✅ WebSocket错误隔离机制存在")
                else:
                    self.diagnosis_results["medium_priority_issues"].append({
                        "type": "WEBSOCKET_ERROR_ISOLATION_MISSING",
                        "severity": "MEDIUM",
                        "location": "websocket/ws_manager.py",
                        "description": "WebSocket错误隔离机制不完善",
                        "fix_required": False
                    })
            
            # 检查各交易所WebSocket实现一致性
            exchanges = ["gate_ws.py", "bybit_ws.py", "okx_ws.py"]
            timestamp_processor_usage = {}
            
            for exchange_file in exchanges:
                file_path = f"websocket/{exchange_file}"
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查时间戳处理器使用方式
                    if "self.timestamp_processor" in content:
                        timestamp_processor_usage[exchange_file] = "instance_method"
                    elif "get_synced_timestamp(" in content:
                        timestamp_processor_usage[exchange_file] = "global_function"
                    else:
                        timestamp_processor_usage[exchange_file] = "unknown"
            
            # 检查一致性
            usage_types = set(timestamp_processor_usage.values())
            if len(usage_types) > 1:
                self.diagnosis_results["high_priority_issues"].append({
                    "type": "WEBSOCKET_TIMESTAMP_INCONSISTENCY",
                    "severity": "HIGH",
                    "location": "websocket/",
                    "description": "三个交易所时间戳处理方式不一致",
                    "evidence": timestamp_processor_usage,
                    "fix_required": True
                })
                print("❌ 发现HIGH问题: 三个交易所时间戳处理方式不一致")
            else:
                print("✅ 三个交易所时间戳处理方式一致")
                
        except Exception as e:
            self.diagnosis_results["critical_issues"].append({
                "type": "WEBSOCKET_DIAGNOSIS_FAILED",
                "severity": "CRITICAL",
                "description": f"WebSocket连接诊断失败: {e}",
                "fix_required": True
            })
    
    async def _diagnose_timestamp_sync(self):
        """诊断时间戳同步问题"""
        print("🔍 3. 诊断时间戳同步...")
        
        try:
            # 尝试导入时间戳处理器
            try:
                from websocket.unified_timestamp_processor import get_timestamp_processor, check_all_timestamp_sync_health, initialize_all_timestamp_processors

                # 🔥 关键修复：先执行时间同步，再检查健康状态
                print("   执行时间同步...")
                sync_results = await initialize_all_timestamp_processors(force_sync=True)

                # 检查所有交易所时间戳同步状态
                health_status = await check_all_timestamp_sync_health()
                
                unhealthy_exchanges = []
                for exchange, status in health_status.items():
                    if not status.get("is_healthy", False):
                        unhealthy_exchanges.append({
                            "exchange": exchange,
                            "time_synced": status.get("time_synced", False),
                            "sync_age_seconds": status.get("sync_age_seconds", -1),
                            "health_level": status.get("health_level", "UNKNOWN")
                        })
                
                if unhealthy_exchanges:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "TIMESTAMP_SYNC_UNHEALTHY",
                        "severity": "HIGH",
                        "location": "websocket/unified_timestamp_processor.py",
                        "description": "时间戳同步状态不健康",
                        "evidence": unhealthy_exchanges,
                        "fix_required": True
                    })
                    print(f"❌ 发现HIGH问题: {len(unhealthy_exchanges)}个交易所时间戳同步不健康")
                else:
                    print("✅ 所有交易所时间戳同步健康")
                    
            except ImportError as e:
                self.diagnosis_results["critical_issues"].append({
                    "type": "TIMESTAMP_PROCESSOR_IMPORT_FAILED",
                    "severity": "CRITICAL",
                    "description": f"时间戳处理器导入失败: {e}",
                    "fix_required": True
                })
                print("❌ 发现CRITICAL问题: 时间戳处理器导入失败")
                
        except Exception as e:
            self.diagnosis_results["critical_issues"].append({
                "type": "TIMESTAMP_DIAGNOSIS_FAILED",
                "severity": "CRITICAL",
                "description": f"时间戳同步诊断失败: {e}",
                "fix_required": True
            })
    
    async def _diagnose_api_rate_limits(self):
        """诊断API限速问题"""
        print("🔍 4. 诊断API限速...")

        try:
            # 检查API调用优化器
            api_optimizer_path = "core/api_call_optimizer.py"
            if os.path.exists(api_optimizer_path):
                with open(api_optimizer_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查OKX限速配置
                if '"okx": 3' in content or '"okx": 2' in content:
                    print("✅ OKX API限速配置存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "OKX_RATE_LIMIT_CONFIG_MISSING",
                        "severity": "HIGH",
                        "location": "core/api_call_optimizer.py",
                        "description": "OKX API限速配置缺失或不合理",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: OKX API限速配置问题")

                # 检查智能缓存机制
                if "cache" in content.lower() and "ttl" in content.lower():
                    print("✅ 智能缓存机制存在")
                else:
                    self.diagnosis_results["medium_priority_issues"].append({
                        "type": "SMART_CACHE_MISSING",
                        "severity": "MEDIUM",
                        "location": "core/api_call_optimizer.py",
                        "description": "智能缓存机制不完善",
                        "fix_required": False
                    })

            # 检查各交易所的限速实现
            exchanges = ["gate_exchange.py", "bybit_exchange.py", "okx_exchange.py"]
            for exchange_file in exchanges:
                file_path = f"exchanges/{exchange_file}"
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查是否有限速控制
                    if "rate_limit" in content.lower() or "sleep" in content:
                        print(f"✅ {exchange_file} 限速控制存在")
                    else:
                        self.diagnosis_results["medium_priority_issues"].append({
                            "type": "EXCHANGE_RATE_LIMIT_MISSING",
                            "severity": "MEDIUM",
                            "location": f"exchanges/{exchange_file}",
                            "description": f"{exchange_file} 缺乏限速控制",
                            "fix_required": False
                        })

        except Exception as e:
            self.diagnosis_results["critical_issues"].append({
                "type": "API_RATE_LIMIT_DIAGNOSIS_FAILED",
                "severity": "CRITICAL",
                "description": f"API限速诊断失败: {e}",
                "fix_required": True
            })

    async def _diagnose_trading_pairs(self):
        """诊断交易对验证问题"""
        print("🔍 5. 诊断交易对验证...")

        try:
            # 检查交易规则预加载器
            preloader_path = "core/trading_rules_preloader.py"
            if os.path.exists(preloader_path):
                with open(preloader_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查交易对验证机制
                if ("_validate_input_parameters" in content and
                    "_validate_symbol_format" in content and
                    "preload_all_trading_rules" in content):
                    print("✅ 交易对验证机制存在")
                else:
                    self.diagnosis_results["high_priority_issues"].append({
                        "type": "TRADING_PAIR_VALIDATION_MISSING",
                        "severity": "HIGH",
                        "location": "core/trading_rules_preloader.py",
                        "description": "交易对验证机制缺失",
                        "fix_required": True
                    })
                    print("❌ 发现HIGH问题: 交易对验证机制缺失")

                # 检查预加载机制
                if "preload" in content.lower() and "cache" in content.lower():
                    print("✅ 交易规则预加载机制存在")
                else:
                    self.diagnosis_results["medium_priority_issues"].append({
                        "type": "TRADING_RULES_PRELOAD_INCOMPLETE",
                        "severity": "MEDIUM",
                        "location": "core/trading_rules_preloader.py",
                        "description": "交易规则预加载机制不完善",
                        "fix_required": False
                    })
            else:
                self.diagnosis_results["critical_issues"].append({
                    "type": "TRADING_RULES_PRELOADER_MISSING",
                    "severity": "CRITICAL",
                    "description": "交易规则预加载器文件不存在",
                    "fix_required": True
                })

        except Exception as e:
            self.diagnosis_results["critical_issues"].append({
                "type": "TRADING_PAIRS_DIAGNOSIS_FAILED",
                "severity": "CRITICAL",
                "description": f"交易对验证诊断失败: {e}",
                "fix_required": True
            })

    async def _diagnose_architecture_consistency(self):
        """诊断系统架构一致性"""
        print("🔍 6. 诊断系统架构一致性...")

        try:
            # 检查统一模块使用情况
            core_modules = [
                "unified_order_spread_calculator.py",
                "unified_balance_manager.py",
                "unified_depth_analyzer.py",
                "unified_opening_manager.py",
                "unified_closing_manager.py"
            ]

            missing_modules = []
            for module in core_modules:
                if not os.path.exists(f"core/{module}"):
                    missing_modules.append(module)

            if missing_modules:
                self.diagnosis_results["high_priority_issues"].append({
                    "type": "UNIFIED_MODULES_MISSING",
                    "severity": "HIGH",
                    "location": "core/",
                    "description": "核心统一模块缺失",
                    "evidence": missing_modules,
                    "fix_required": True
                })
                print(f"❌ 发现HIGH问题: {len(missing_modules)}个核心统一模块缺失")
            else:
                print("✅ 核心统一模块完整")

            # 检查WebSocket统一模块
            websocket_modules = [
                "unified_timestamp_processor.py",
                "unified_data_formatter.py",
                "unified_connection_pool_manager.py"
            ]

            missing_ws_modules = []
            for module in websocket_modules:
                if not os.path.exists(f"websocket/{module}"):
                    missing_ws_modules.append(module)

            if missing_ws_modules:
                self.diagnosis_results["high_priority_issues"].append({
                    "type": "WEBSOCKET_UNIFIED_MODULES_MISSING",
                    "severity": "HIGH",
                    "location": "websocket/",
                    "description": "WebSocket统一模块缺失",
                    "evidence": missing_ws_modules,
                    "fix_required": True
                })
                print(f"❌ 发现HIGH问题: {len(missing_ws_modules)}个WebSocket统一模块缺失")
            else:
                print("✅ WebSocket统一模块完整")

        except Exception as e:
            self.diagnosis_results["critical_issues"].append({
                "type": "ARCHITECTURE_DIAGNOSIS_FAILED",
                "severity": "CRITICAL",
                "description": f"系统架构一致性诊断失败: {e}",
                "fix_required": True
            })

    async def _generate_diagnosis_report(self):
        """生成诊断报告"""
        print("🔍 7. 生成诊断报告...")

        # 计算系统健康度
        total_issues = (len(self.diagnosis_results["critical_issues"]) +
                       len(self.diagnosis_results["high_priority_issues"]) +
                       len(self.diagnosis_results["medium_priority_issues"]))

        critical_count = len(self.diagnosis_results["critical_issues"])
        high_count = len(self.diagnosis_results["high_priority_issues"])

        # 健康度评分 (100分制)
        health_score = 100
        health_score -= critical_count * 30  # CRITICAL问题扣30分
        health_score -= high_count * 15      # HIGH问题扣15分
        health_score -= len(self.diagnosis_results["medium_priority_issues"]) * 5  # MEDIUM问题扣5分
        health_score = max(0, health_score)  # 最低0分

        self.diagnosis_results["system_health"] = {
            "overall_score": health_score,
            "health_level": "CRITICAL" if health_score < 50 else "WARNING" if health_score < 80 else "GOOD",
            "total_issues": total_issues,
            "critical_issues_count": critical_count,
            "high_priority_issues_count": high_count,
            "medium_priority_issues_count": len(self.diagnosis_results["medium_priority_issues"])
        }

        # 生成修复建议
        if critical_count > 0:
            self.diagnosis_results["recommendations"].append({
                "priority": "IMMEDIATE",
                "action": "立即修复所有CRITICAL问题",
                "description": "系统存在严重问题，需要立即修复以确保正常运行"
            })

        if high_count > 0:
            self.diagnosis_results["recommendations"].append({
                "priority": "HIGH",
                "action": "优先修复HIGH优先级问题",
                "description": "这些问题影响系统性能和稳定性，建议优先处理"
            })

        if health_score < 80:
            self.diagnosis_results["recommendations"].append({
                "priority": "MEDIUM",
                "action": "系统整体优化",
                "description": "建议进行系统整体优化，提升健康度到80分以上"
            })

        print(f"✅ 系统健康度评分: {health_score}/100 ({self.diagnosis_results['system_health']['health_level']})")

async def main():
    """主函数"""
    diagnosis = ComprehensiveSystemDiagnosis()
    results = await diagnosis.run_full_diagnosis()
    
    print("\n" + "=" * 60)
    print("🎯 诊断结果汇总:")
    print(f"CRITICAL问题: {len(results['critical_issues'])}")
    print(f"HIGH优先级问题: {len(results['high_priority_issues'])}")
    print(f"MEDIUM优先级问题: {len(results['medium_priority_issues'])}")
    
    # 保存诊断结果
    os.makedirs("../diagnostic_scripts", exist_ok=True)
    with open("../diagnostic_scripts/diagnosis_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print("\n📋 详细诊断结果已保存到: ../diagnostic_scripts/diagnosis_results.json")

if __name__ == "__main__":
    asyncio.run(main())
