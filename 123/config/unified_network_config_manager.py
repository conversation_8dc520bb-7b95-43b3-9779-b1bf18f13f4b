# -*- coding: utf-8 -*-
"""
🔥 统一网络配置管理器
确保三交易所网络配置一致性，支持任意代币的期货溢价套利
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class NetworkConfig:
    """网络配置"""
    timeout_seconds: int = 30
    max_retries: int = 3
    retry_interval_seconds: float = 1.0
    connection_pool_size: int = 10
    keep_alive: bool = True
    ssl_verify: bool = True
    user_agent: str = "ArbitrageBot/1.0"

@dataclass
class ExchangeNetworkConfig:
    """交易所网络配置"""
    base_url: str
    websocket_url: str
    api_timeout: int = 10
    websocket_timeout: int = 30
    rate_limit_per_second: float = 2.0
    max_connections: int = 5

class UnifiedNetworkConfigManager:
    """🔥 统一网络配置管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._network_config = None
        self._exchange_configs = {}
        self._load_configurations()
        
    def _load_configurations(self):
        """加载网络配置"""
        try:
            # 🔥 统一网络配置
            self._network_config = NetworkConfig(
                timeout_seconds=int(os.getenv("NETWORK_TIMEOUT", "30")),
                max_retries=int(os.getenv("NETWORK_MAX_RETRIES", "3")),
                retry_interval_seconds=float(os.getenv("NETWORK_RETRY_INTERVAL", "1.0")),
                connection_pool_size=int(os.getenv("CONNECTION_POOL_SIZE", "10")),
                keep_alive=os.getenv("KEEP_ALIVE", "true").lower() == "true",
                ssl_verify=os.getenv("SSL_VERIFY", "true").lower() == "true",
                user_agent=os.getenv("USER_AGENT", "ArbitrageBot/1.0")
            )
            
            # 🔥 三交易所统一配置
            self._exchange_configs = {
                "gate": ExchangeNetworkConfig(
                    base_url=os.getenv("GATE_BASE_URL", "https://api.gateio.ws"),
                    websocket_url=os.getenv("GATE_WS_URL", "wss://api.gateio.ws/ws/v4/"),
                    api_timeout=int(os.getenv("GATE_API_TIMEOUT", "10")),
                    websocket_timeout=int(os.getenv("GATE_WS_TIMEOUT", "30")),
                    rate_limit_per_second=float(os.getenv("GATE_RATE_LIMIT", "2.0")),
                    max_connections=int(os.getenv("GATE_MAX_CONNECTIONS", "5"))
                ),
                "bybit": ExchangeNetworkConfig(
                    base_url=os.getenv("BYBIT_BASE_URL", "https://api.bybit.com"),
                    websocket_url=os.getenv("BYBIT_WS_URL", "wss://stream.bybit.com/v5/public/spot"),
                    api_timeout=int(os.getenv("BYBIT_API_TIMEOUT", "10")),
                    websocket_timeout=int(os.getenv("BYBIT_WS_TIMEOUT", "30")),
                    rate_limit_per_second=float(os.getenv("BYBIT_RATE_LIMIT", "2.0")),
                    max_connections=int(os.getenv("BYBIT_MAX_CONNECTIONS", "5"))
                ),
                "okx": ExchangeNetworkConfig(
                    base_url=os.getenv("OKX_BASE_URL", "https://www.okx.com"),
                    websocket_url=os.getenv("OKX_WS_URL", "wss://ws.okx.com:8443/ws/v5/public"),
                    api_timeout=int(os.getenv("OKX_API_TIMEOUT", "10")),
                    websocket_timeout=int(os.getenv("OKX_WS_TIMEOUT", "30")),
                    rate_limit_per_second=float(os.getenv("OKX_RATE_LIMIT", "1.5")),  # 🔥 OKX更严格限速
                    max_connections=int(os.getenv("OKX_MAX_CONNECTIONS", "3"))  # 🔥 OKX连接数限制
                )
            }
            
            self.logger.info("✅ 统一网络配置加载完成")
            self.logger.info(f"   网络超时: {self._network_config.timeout_seconds}秒")
            self.logger.info(f"   连接池大小: {self._network_config.connection_pool_size}")
            self.logger.info(f"   三交易所配置: {len(self._exchange_configs)}个")
            
        except Exception as e:
            self.logger.error(f"❌ 网络配置加载失败: {e}")
            # 使用默认配置
            self._network_config = NetworkConfig()
            self._exchange_configs = {}
            
    def get_network_config(self) -> NetworkConfig:
        """获取统一网络配置"""
        return self._network_config
        
    def get_exchange_config(self, exchange_name: str) -> Optional[ExchangeNetworkConfig]:
        """获取交易所网络配置"""
        return self._exchange_configs.get(exchange_name.lower())
        
    def get_all_exchange_configs(self) -> Dict[str, ExchangeNetworkConfig]:
        """获取所有交易所网络配置"""
        return self._exchange_configs.copy()
        
    def update_exchange_config(self, exchange_name: str, config: ExchangeNetworkConfig):
        """更新交易所网络配置"""
        self._exchange_configs[exchange_name.lower()] = config
        self.logger.info(f"✅ {exchange_name}网络配置已更新")
        
    def get_rate_limit(self, exchange_name: str) -> float:
        """获取交易所限速配置"""
        config = self.get_exchange_config(exchange_name)
        return config.rate_limit_per_second if config else 2.0
        
    def get_max_connections(self, exchange_name: str) -> int:
        """获取交易所最大连接数"""
        config = self.get_exchange_config(exchange_name)
        return config.max_connections if config else 5
        
    def get_websocket_url(self, exchange_name: str) -> Optional[str]:
        """获取WebSocket URL"""
        config = self.get_exchange_config(exchange_name)
        return config.websocket_url if config else None
        
    def get_api_base_url(self, exchange_name: str) -> Optional[str]:
        """获取API基础URL"""
        config = self.get_exchange_config(exchange_name)
        return config.base_url if config else None
        
    def validate_configuration(self) -> bool:
        """验证配置完整性"""
        try:
            if not self._network_config:
                self.logger.error("❌ 统一网络配置缺失")
                return False
                
            required_exchanges = ["gate", "bybit", "okx"]
            for exchange in required_exchanges:
                if exchange not in self._exchange_configs:
                    self.logger.error(f"❌ {exchange}交易所配置缺失")
                    return False
                    
            self.logger.info("✅ 网络配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 配置验证异常: {e}")
            return False
            
    def get_timeout_config(self, exchange_name: str, operation_type: str = "api") -> int:
        """获取超时配置"""
        config = self.get_exchange_config(exchange_name)
        if not config:
            return self._network_config.timeout_seconds
            
        if operation_type == "websocket":
            return config.websocket_timeout
        else:
            return config.api_timeout

# 🔥 全局实例
_network_config_manager = None

def get_unified_network_config_manager() -> UnifiedNetworkConfigManager:
    """获取统一网络配置管理器实例"""
    global _network_config_manager
    if _network_config_manager is None:
        _network_config_manager = UnifiedNetworkConfigManager()
    return _network_config_manager

def get_exchange_rate_limit(exchange_name: str) -> float:
    """快速获取交易所限速配置"""
    manager = get_unified_network_config_manager()
    return manager.get_rate_limit(exchange_name)

def get_exchange_websocket_url(exchange_name: str) -> Optional[str]:
    """快速获取WebSocket URL"""
    manager = get_unified_network_config_manager()
    return manager.get_websocket_url(exchange_name)
